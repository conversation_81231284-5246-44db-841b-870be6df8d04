const axios = require('axios');
require('dotenv').config();

// Cấu hình API
const API_URL = process.env.API_URL || 'https://trustmove.vn';

/**
 * Lấy admin token bằng cách đăng nhập
 */
async function getAdminToken() {
    console.log('=== LẤY ADMIN TOKEN ===');
    console.log(`API URL: ${API_URL}`);
    
    try {
        console.log('Đang đăng nhập với tài khoản admin...');
        
        const loginResponse = await axios.post(`${API_URL}/api/admin/login`, {
            username: 'trustmove',
            password: 'england1'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        if (loginResponse.data && loginResponse.data.token) {
            console.log('✅ Đăng nhập thành công!');
            console.log('Admin Token:', loginResponse.data.token);
            
            // Lưu token vào file .env
            const fs = require('fs');
            const envContent = fs.readFileSync('.env', 'utf8');
            
            if (envContent.includes('ADMIN_TOKEN=')) {
                // Cập nhật token hiện có
                const updatedContent = envContent.replace(/ADMIN_TOKEN=.*/, `ADMIN_TOKEN=${loginResponse.data.token}`);
                fs.writeFileSync('.env', updatedContent);
            } else {
                // Thêm token mới
                fs.appendFileSync('.env', `\n# Admin Token for testing\nADMIN_TOKEN=${loginResponse.data.token}\n`);
            }
            
            console.log('✅ Đã lưu token vào file .env');
            return loginResponse.data.token;
            
        } else {
            throw new Error('Không nhận được token từ server');
        }
        
    } catch (error) {
        console.error('❌ Lỗi khi đăng nhập:');
        
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        } else {
            console.error('Error:', error.message);
        }
        
        throw error;
    }
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
    getAdminToken().catch(console.error);
}

module.exports = { getAdminToken };
